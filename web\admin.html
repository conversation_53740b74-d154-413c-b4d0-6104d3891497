<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信令服务器管理控制台 v1.0.7</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: visible; /* 允许下拉菜单超出容器边界 */
            min-height: 100vh; /* 使用最小高度而不是固定高度 */
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .status-panel {
            grid-column: 1 / -1;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .receivers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .senders-grid {
            margin-top: 20px;
        }

        .ip-group {
            margin-bottom: 30px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .ip-group-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f1f3f4;
        }

        .ip-group-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }

        .ip-group-count {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }

        /* 域名分组样式 */
        .domain-group {
            margin-bottom: 30px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .domain-group-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }

        .domain-group-title {
            font-size: 1.3em;
            font-weight: 700;
            color: #495057;
            margin: 0;
        }

        .domain-group-count {
            background: #667eea;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            margin-left: auto;
            font-weight: 600;
        }

        /* 分类分组样式 */
        .category-group {
            margin-bottom: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }

        .category-group-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .category-group-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #6c757d;
            margin: 0;
        }

        .category-group-count {
            background: #28a745;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: auto;
            font-weight: 500;
        }

        .category-devices {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
        }

        /* 房间信息样式 */
        .room-info {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 0.9em;
            margin-bottom: 10px;
            font-weight: 500;
        }

        /* 设备基本信息样式 */
        .device-basic-info {
            background: #f1f3f4;
            padding: 8px 10px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-size: 0.85em;
        }

        .device-id {
            color: #495057;
            font-weight: 600;
        }

        .device-ip {
            color: #6c757d;
            margin-top: 2px;
        }

        /* 设备进度显示样式 */
        .device-progress {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #17a2b8;
        }

        .progress-item {
            font-size: 0.85em;
            line-height: 1.4;
        }

        .progress-item.success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }

        .progress-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .progress-item .command {
            font-weight: 600;
            margin-right: 8px;
        }

        .progress-item .timestamp {
            float: right;
            color: #6c757d;
            font-size: 0.8em;
        }

        /* 连接状态指示器 */
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .connection-status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .connection-status.error {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .ip-devices {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
        }

        .receiver-card, .sender-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 18px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .receiver-card:hover, .sender-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.12);
        }

        .receiver-card h3, .sender-card h3 {
            color: #495057;
            margin-bottom: 12px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .device-info {
            margin-bottom: 10px;
        }

        .device-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            font-size: 0.9em;
        }

        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }

        .device-info-value {
            color: #495057;
            font-weight: 600;
        }

        .game-name {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .last-online {
            color: #28a745;
            font-size: 0.85em;
        }

        .last-online.offline {
            color: #dc3545;
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-success {
            background: rgba(72, 187, 120, 0.2);
        }

        .log-error {
            background: rgba(245, 101, 101, 0.2);
        }

        .log-info {
            background: rgba(66, 153, 225, 0.2);
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
        }

        /* 设备控制卡片样式 */
        .device-controls {
            margin-top: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-main-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.2s;
            flex: 1;
        }

        .control-main-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
        }

        .control-dropdown {
            position: relative;
            display: inline-block;
        }

        /* 删除重复的dropdown-toggle定义，保留下面统一的定义 */

        .dropdown-menu {
            position: fixed; /* 使用fixed定位避免被父容器裁剪 */
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw; /* 限制最大宽度避免超出屏幕 */
            z-index: 999999; /* 提高到最高层级，确保在按钮之上 */
            display: none;
            padding: 15px;
            /* 移除可能导致冲突的transform和transition */
        }

        /* 下拉菜单显示状态 */
        .dropdown-menu.show {
            display: block !important;
        }

        /* 确保下拉菜单容器有正确的定位 */
        .control-dropdown {
            position: relative;
            z-index: 1000;
        }

        /* 下拉按钮样式优化 */
        .dropdown-toggle {
            position: relative;
            z-index: 100000; /* 确保按钮在下拉菜单之上 */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .dropdown-toggle:hover:not(.active) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .dropdown-toggle.active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            transform: none !important; /* 激活状态时禁用transform，防止位置变化 */
        }

        /* 删除重复的dropdown-menu.show定义，保留上面带!important的版本 */

        .dropdown-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .dropdown-section {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f1f3f4;
        }

        .dropdown-section:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }

        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }

        .dropdown-btn.btn-success {
            color: #28a745;
        }

        .dropdown-btn.btn-warning {
            color: #ffc107;
        }

        .dropdown-btn.btn-danger {
            color: #dc3545;
        }

        .dropdown-btn.btn-info {
            color: #17a2b8;
        }

        .dropdown-btn.btn-primary {
            color: #007bff;
        }

        /* 参数输入框 */
        .param-input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 5px;
        }

        .param-group {
            margin-bottom: 8px;
        }

        .param-group label {
            display: block;
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 3px;
        }

        /* 模态对话框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal-overlay.show {
            display: flex;
            opacity: 1;
        }

        .modal-dialog {
            background: white;
            border-radius: 12px;
            padding: 25px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .modal-overlay.show .modal-dialog {
            transform: scale(1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #6c757d;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .modal-close:hover {
            background-color: #f8f9fa;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding-top: 15px;
            border-top: 1px solid #f1f3f4;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-col {
            flex: 1;
        }

        .form-col label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .form-col input,
        .form-col select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-col input:focus,
        .form-col select:focus {
            outline: none;
            border-color: #667eea;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .checkbox-wrapper input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        /* 消息提示框样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 3000;
        }

        .toast {
            background: white;
            border-radius: 8px;
            padding: 15px 20px;
            margin-bottom: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            border-left: 4px solid #28a745;
            min-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.success {
            border-left-color: #28a745;
        }

        .toast.error {
            border-left-color: #dc3545;
        }

        /* 统计摘要样式 */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stats-summary {
            display: flex;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 15px;
            border-radius: 8px;
            min-width: 60px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-item.online {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .stat-item.offline {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
        }

        .stat-item.total {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 2px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            transition: transform 0.2s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* 域名统计样式 */
        .domain-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .domain-title-section {
            display: flex;
            align-items: center;
        }

        .domain-group-title {
            margin: 0;
            color: #495057;
            font-size: 16px;
        }

        .domain-stats {
            display: flex;
            gap: 10px;
        }

        .domain-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px 10px;
            border-radius: 6px;
            min-width: 45px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .domain-stat-item.online {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .domain-stat-item.offline {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }

        .domain-stat-item.total {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .domain-stat-label {
            font-size: 10px;
            opacity: 0.9;
            margin-bottom: 1px;
        }

        .domain-stat-value {
            font-size: 14px;
            font-weight: bold;
        }

        /* 分类统计样式 */
        .category-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px 15px;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 6px;
            border-left: 3px solid #28a745;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .category-title-section {
            display: flex;
            align-items: center;
        }

        .category-group-title {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }

        .category-stats {
            display: flex;
            gap: 8px;
        }

        .category-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            min-width: 35px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.08);
        }

        .category-stat-item.online {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .category-stat-item.offline {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }

        .category-stat-item.total {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .category-stat-label {
            font-size: 9px;
            opacity: 0.9;
            margin-bottom: 1px;
        }

        .category-stat-value {
            font-size: 12px;
            font-weight: bold;
        }

        /* 媒体显示区域样式 */
        .media-area {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .screenshot-preview {
            background: white;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .screenshot-preview img {
            max-width: 100%;
            height: auto;
            max-height: 200px;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .screenshot-preview img:hover {
            transform: scale(1.02);
        }

        .screenshot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .screenshot-actions {
            margin-top: 8px;
            text-align: center;
        }

        .screenshot-actions button {
            background: none;
            border: none;
            color: #28a745;
            cursor: pointer;
            text-decoration: none;
        }

        .screenshot-actions button:hover {
            text-decoration: underline;
        }

        .log-download-notification {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }

        .log-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .log-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }

        .log-link:hover {
            text-decoration: underline;
        }

        /* 视频预览样式 */
        .video-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 4px;
            overflow: hidden;
        }

        .video-status {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10;
        }

        .video-controls {
            margin-top: 8px;
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .video-controls .btn {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }

        .video-controls .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }

        .video-controls .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
        }

        /* 设备菜单按钮样式增强 */
        .device-menu button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .device-menu button:disabled:hover {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        /* 批量操作按钮样式 */
        .domain-batch-controls {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .batch-btn {
            padding: 6px 12px;
            font-size: 12px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: #f8f9fa;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .batch-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
        }

        .batch-btn:active {
            transform: translateY(0);
        }

        /* 不同类型的批量按钮颜色 */
        .batch-btn:nth-child(1) {
            /* 视频预览 - 蓝色 */
            border-color: #007bff;
            color: #007bff;
        }

        .batch-btn:nth-child(1):hover {
            background: #007bff;
            color: white;
        }

        .batch-btn:nth-child(2) {
            /* 截屏 - 绿色 */
            border-color: #28a745;
            color: #28a745;
        }

        .batch-btn:nth-child(2):hover {
            background: #28a745;
            color: white;
        }

        .batch-btn:nth-child(3) {
            /* 下载日志 - 橙色 */
            border-color: #fd7e14;
            color: #fd7e14;
        }

        .batch-btn:nth-child(3):hover {
            background: #fd7e14;
            color: white;
        }
web\admin.html
        .toast.warning {
            border-left-color: #ffc107;
        }

        .toast.info {
            border-left-color: #17a2b8;
        }

        .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .toast-title {
            font-weight: 600;
            color: #495057;
        }

        .toast-close {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            font-size: 18px;
        }

        .toast-body {
            color: #6c757d;
            font-size: 14px;
        }

        /* ===== 新的设备菜单系统 ===== */
        .device-menu-container {
            position: relative;
            display: inline-block;
        }

        .device-menu-trigger {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s ease;
            position: relative;
            z-index: 10;
        }

        .device-menu-trigger:hover:not(.menu-active) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .device-menu-trigger.menu-active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .device-menu-panel {
            position: fixed;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw;
            z-index: 999999;
            display: none;
            padding: 15px;
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 0.15s ease, transform 0.15s ease;
        }

        .device-menu-panel.menu-visible {
            display: block;
            opacity: 1;
            transform: scale(1);
        }

        .device-menu-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .device-menu-section {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f1f3f4;
        }

        .device-menu-section:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .device-menu-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .device-menu-item {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
            color: #495057;
        }

        .device-menu-item:hover {
            background-color: #f8f9fa;
        }

        .device-menu-item.item-success {
            color: #28a745;
        }

        .device-menu-item.item-warning {
            color: #ffc107;
        }

        .device-menu-item.item-danger {
            color: #dc3545;
        }

        .device-menu-item.item-info {
            color: #17a2b8;
        }

        .device-menu-item.item-primary {
            color: #007bff;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .device-menu-panel {
                min-width: 280px;
                max-width: 95vw;
            }

            .device-menu-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .dropdown-menu {
                min-width: 250px !important;
                max-width: 90vw !important;
                /* 移除固定的left: 0，让JavaScript动态设置位置 */
                right: auto;
                /* 确保响应式状态下也使用JavaScript位置 */
                left: auto !important;
                top: auto !important;
            }

            .device-controls {
                flex-direction: column;
                gap: 8px;
            }

            .control-main-btn {
                width: 100%;
            }

            .receivers-grid {
                grid-template-columns: 1fr;
            }

            .receiver-card {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 信令服务器管理控制台</h1>
            <p>WebRTC信令服务器远程管理和监控平台</p>
        </div>

        <!-- 连接状态指示器 -->
        <div id="connectionStatus" class="connection-status disconnected">连接中...</div>

        <div class="main-content">
            <!-- 服务器配置管理 -->
            <div class="section">
                <h2>📡 服务器配置管理</h2>
                
                <div class="form-group">
                    <label>STUN服务器 (每行一个)</label>
                    <textarea id="stunServers" rows="4" placeholder="stun:stun.l.google.com:19302&#10;stun:stun1.l.google.com:19302"></textarea>
                </div>

                <div class="form-group">
                    <label>TURN服务器配置 (JSON格式)</label>
                    <textarea id="turnServers" rows="6" placeholder='[{"urls": "turn:numb.viagenie.ca", "username": "<EMAIL>", "credential": "muazkh"}]'></textarea>
                </div>

                <button class="btn" onclick="updateConfig()">更新配置</button>
                <button class="btn btn-success" onclick="updateConfigAndBroadcast()">更新并广播</button>
                <button class="btn btn-info" onclick="broadcastConfig()">广播配置</button>
                <button class="btn btn-warning" onclick="loadCurrentConfig()">加载当前配置</button>
            </div>

            <!-- 发送端控制 -->
            <div class="section">
                <h2>🎮 发送端控制</h2>

                <div class="form-group">
                    <label>目标发送端ID</label>
                    <input type="text" id="targetSender" placeholder="gamev-8cd7c032">
                </div>

                <div class="form-group">
                    <label>控制命令</label>
                    <select id="controlCommand">
                        <option value="start_service">开始服务</option>
                        <option value="stop_service">停止服务</option>
                        <option value="restart_service">重启服务</option>
                        <option value="change_resolution">改变分辨率</option>
                        <option value="change_bitrate">改变码率</option>
                        <option value="change_codec">改变编码</option>
                        <option value="reboot_device">重启设备</option>
                        <option value="set_auto_start_game">设置自动启动游戏</option>
                        <option value="toggle_log_display">开关日志输出</option>
                        <option value="download_logs">下载最近日志</option>
                        <option value="take_screenshot">截屏</option>
                        <option value="upgrade">升级应用</option>
                    </select>
                    <div id="commonTags" style="margin-top:8px;">
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("分辨率1080P", "{\"resolution\":\"1920x1080\"}")'>分辨率1080P</span>
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("分辨率720P", "{\"resolution\":\"1280x720\"}")'>分辨率720P</span>
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("码率3000", "{\"bitrate\":3000}")'>码率3000</span>
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("码率1500", "{\"bitrate\":1500}")'>码率1500</span>
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("H264编码", "{\"codec\":\"H264\"}")'>H264编码</span>
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("VP8编码", "{\"codec\":\"VP8\"}")'>VP8编码</span>
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("强制升级", "{\"force\":true}")'>强制升级</span>
                        <span class="btn btn-info" style="padding:4px 10px;font-size:12px;margin-bottom:4px;" onclick='fillParamTag("默认升级参数", "{\"apk_url\":\"http://example.com/app.apk\",\"version\":\"2.0.0\",\"force\":false}")'>默认升级参数</span>
                    </div>
                </div>

                <div class="form-group">
                    <label>命令参数 (JSON格式)</label>
                    <textarea id="commandParams" rows="3" placeholder='{"resolution": "1920x1080", "bitrate": 3000}'></textarea>
                </div>

                <div class="form-group">
                    <label>设备ID (用于签名验证)</label>
                    <input type="text" id="deviceId" placeholder="设备机器码MD5">
                </div>

                <button class="btn btn-success" onclick="sendCommand()">发送命令</button>
                <button class="btn btn-info" onclick="refreshSenders()">刷新发送端列表</button>
            </div>

            <!-- 升级管理 -->
            <div class="section">
                <h2>📦 升级管理</h2>
                
                <div class="form-group">
                    <label>APK下载地址</label>
                    <input type="text" id="apkUrl" placeholder="https://example.com/app-v2.0.apk" value="http://39.96.165.173:8888/down/XdFBq5q171Qu.apk">
                </div>

                <div class="form-group">
                    <label>版本号</label>
                    <input type="text" id="version" placeholder="2.0.0" value="2">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="forceUpgrade"> 强制升级
                    </label>
                </div>

                <div class="form-group">
                    <label>目标发送端</label>
                    <input type="text" id="upgradeTarget" placeholder="gamev-8cd7c032 或留空表示所有">
                </div>

                <button class="btn btn-warning" onclick="sendUpgrade()">发送升级命令</button>
            </div>

            <!-- 批量操作 -->
            <div class="section">
                <h2>🔄 批量操作</h2>

                <div class="form-group">
                    <label>选择发送端</label>
                    <div id="sendersCheckbox" class="checkbox-group">
                        <!-- 动态生成发送端复选框 -->
                    </div>
                </div>
                <div class="form-group">
                    <label>手工输入发送端ID (每行一个)</label>
                    <textarea id="manualSenderIds" rows="3" placeholder="gamev-xxxx\ngamev-yyyy"></textarea>
                </div>
                <div class="form-group">
                    <label>批量发送间隔(秒)</label>
                    <input type="number" id="batchInterval" min="0" value="0" style="width:100px;">
                </div>
                <button class="btn btn-info" onclick="sendConfigToSelected()">向选中设备发送配置</button>
                <button class="btn btn-success" onclick="batchCommand()">批量发送命令</button>
            </div>
        </div>

        <!-- 状态监控面板 -->
        <div class="status-panel">
            <div class="section-header">
                <h2>📊 发送端状态监控</h2>
                <div class="stats-summary">
                    <div class="stat-item online">
                        <span class="stat-label">在线</span>
                        <span class="stat-value" id="onlineCount">0</span>
                    </div>
                    <div class="stat-item offline">
                        <span class="stat-label">离线</span>
                        <span class="stat-value" id="offlineCount">0</span>
                    </div>
                    <div class="stat-item total">
                        <span class="stat-label">总计</span>
                        <span class="stat-value" id="totalCount">0</span>
                    </div>
                </div>
            </div>
            <div id="sendersStatus" class="senders-grid">
                <!-- 动态生成发送端状态卡片，按IP分类 -->
            </div>
        </div>

        <!-- 接收端状态面板 -->
        <div class="status-panel">
            <h2>📱 接收端状态监控</h2>
            <div id="receiversStatus" class="receivers-grid">
                <!-- 动态生成接收端状态卡片 -->
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="status-panel">
            <h2>📝 操作日志</h2>
            <div id="logArea" class="log-area">
                <div class="log-entry log-info">[系统] 管理控制台已加载</div>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div class="modal-overlay" id="commandModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">命令设置</h3>
                <button class="modal-close" onclick="admin.closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="admin.closeModal()">取消</button>
                <button class="btn btn-success" id="modalConfirm" onclick="admin.confirmCommand()">确定</button>
            </div>
        </div>
    </div>

    <!-- STUN/TURN配置模态框 -->
    <div class="modal-overlay" id="stunTurnModal">
        <div class="modal-dialog" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">🌐 STUN/TURN服务器配置</h3>
                <button class="modal-close" onclick="admin.closeStunTurnModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>常用STUN服务器配置</label>
                    <select id="stunPresets" onchange="admin.applyStunPreset()">
                        <option value="">选择预设配置</option>
                        <option value="google">Google STUN服务器</option>
                        <option value="mozilla">Mozilla STUN服务器</option>
                        <option value="custom">自定义配置</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>STUN服务器 (每行一个)</label>
                    <textarea id="modalStunServers" rows="4" placeholder="stun:stun.l.google.com:19302&#10;stun:stun1.l.google.com:19302"></textarea>
                </div>

                <div class="form-group">
                    <label>常用TURN服务器配置</label>
                    <select id="turnPresets" onchange="admin.applyTurnPreset()">
                        <option value="">选择预设配置</option>
                        <option value="numb">Numb TURN服务器</option>
                        <option value="xirsys">Xirsys TURN服务器</option>
                        <option value="custom">自定义配置</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>TURN服务器配置 (JSON格式)</label>
                    <textarea id="modalTurnServers" rows="6" placeholder='[{"urls": "turn:numb.viagenie.ca", "username": "<EMAIL>", "credential": "muazkh"}]'></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="admin.closeStunTurnModal()">取消</button>
                <button class="btn btn-success" onclick="admin.saveStunTurnConfig()">保存配置</button>
                <button class="btn btn-info" onclick="admin.saveAndBroadcastStunTurn()">保存并广播</button>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div class="toast-container" id="toastContainer">
        <!-- 动态生成的提示消息 -->
    </div>

    <script src="admin.js"></script>
</body>
</html>
